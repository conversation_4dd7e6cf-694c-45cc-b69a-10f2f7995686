{"hash": "e56f7384", "browserHash": "879de83f", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "da966c7c", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "30434d15", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0b721f57", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "a7bf1022", "needsInterop": true}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "27979b87", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.mjs", "file": "lucide-react.js", "fileHash": "826691fc", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "3d40ea05", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "1addaa56", "needsInterop": false}}, "chunks": {"browser-ZONL5W77": {"file": "browser-ZONL5W77.js"}, "chunk-QH3POG6S": {"file": "chunk-QH3POG6S.js"}, "chunk-G52XTN3B": {"file": "chunk-G52XTN3B.js"}, "chunk-LXGCQ6UQ": {"file": "chunk-LXGCQ6UQ.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}