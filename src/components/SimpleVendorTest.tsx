import React, { useState, useEffect } from 'react';
import { getVendors } from '../lib/supabase';

const SimpleVendorTest: React.FC = () => {
  const [vendors, setVendors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadVendors = async () => {
      try {
        console.log('SimpleVendorTest: Loading vendors...');
        const vendorData = await getVendors();
        console.log('SimpleVendorTest: Received vendors:', vendorData);
        setVendors(vendorData);
        setError(null);
      } catch (err) {
        console.error('SimpleVendorTest: Error:', err);
        setError('Failed to load vendors');
        setVendors([]);
      } finally {
        setLoading(false);
      }
    };

    loadVendors();
  }, []);

  if (loading) {
    return <div>Loading vendors...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div>
      <h2>Simple Vendor Test</h2>
      <p>Found {vendors.length} vendors</p>
      {vendors.map((vendor) => (
        <div key={vendor.id} style={{ border: '1px solid #ccc', margin: '10px', padding: '10px' }}>
          <h3>{vendor.name}</h3>
          <p>{vendor.description}</p>
          <p>Rating: {vendor.rating}</p>
          <p>Verified: {vendor.verified ? 'Yes' : 'No'}</p>
        </div>
      ))}
    </div>
  );
};

export default SimpleVendorTest;
